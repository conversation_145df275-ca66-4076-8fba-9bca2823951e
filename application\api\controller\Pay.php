<?php

namespace app\api\controller;

use app\common\controller\Api;
use app\common\library\Ems;
use app\common\library\Sms;
use fast\Random;
use think\Validate;
use weixin\WXBizDataCrypt;
use weixin\WeixinPay;
use think\Db;

/**
 * 订单接口
 */
class Pay extends Api
{
    protected $noNeedLogin = ['notify','refundnotify','refundCost','share'];
    protected $noNeedRight = '*';

    public function _initialize()
    {
        parent::_initialize();
    }
    
    //退款回调
    public function refundnotify() {
        $xml = file_get_contents("php://input");
        //$xml = '<xml><return_code>SUCCESS</return_code><appid><![CDATA[wxe2946cbf308a8915]]></appid><mch_id><![CDATA[1600070586]]></mch_id><nonce_str><![CDATA[ce407affc8ce8aa9371b2857fe1401ed]]></nonce_str><req_info><![CDATA[yhNSDgsfVOlt1tQKuuVAyxgvwz36TSnez2vqwF5x5ewUt7L03hOYlXuukFUQhrBXz5lDSM06ZLuZLIy0bsoQuAb9COI2vHCpNW8x1+GGCyETt05YMMT2xpkbed+CwbFs8Amj2N4zeWlEGqC23uQ4Qh1Fx5Pd1+XnkqbvOMzuC+GYpGjp8ykUOHZEU/LScJHZnxJHL16zA1sMHGKZ6BK3KH4R+MUigLdMe0SeJU8KktXccGA6nyvSmdaJNzub4FzG7j2D0/PyGosnPtXegMs+KbWDa+P6iSt0Hzt+DIjp4/f7BujMXIXttmOVlKTB/H3ELxqSOnqY5UfAxo0tcCo/HrMio3jS4g3RkKbNAfG6llFxwCHOvy6r6t0rByV3UMEgrpOb7VskZFRU4nmgwPF/PZUkEHFatrBy8RxFKcVtbkeB/jaD6Glf53ANWt/9/6CTUTyNmGVr6FRMgOeNOreHUPuvQ+gaEijFWnsMkWHQAQD5ZFERKqJrdfXYJHeqnL+3XuDCYaxyUAs1C/ZV0vAYXiOdtMDuRK5PbIXRqfLXQjxczMDhRUNST9VGvG4HUe12vaJuJQ1BmrLcpnlKSVMPTR6nhXmOwbX+6njJXgYVTBpISGpc4CDx+jGh8+VBEWn9IORSyVh1SxQh/wSkLkKlPAggvZXrx4d/6bFRBGbgKjUBKvOxfn+NoGAjUrd5HLO0b87Vq1wK7Eg4x6MCtrUl4kcr+UdghZIltNL1yn+5SpZTaVF6Ulz5nrGuM0iG8Y/CW8wQA+izewnjYc3Gf4v0fppyotM48FRg+otSfJ2PfxJWstKDatHdY1CDrxjk/Nyd/xsVDSqZYu7jaKmYjLrtFPtDo9DtTWwHUBKNhsSIeDDh330LPHDaCBH7rmoe51KqdpQF6tIKThKRjlJVPLJ7lx6F1C/gylshZPB/hAIOcRyFBleW6RHlDnbHdJ7IRMamLK0wMzOQQutbkda7H3fHOy5Qcyn37+qtmdSJwgZzo4iKH0X/1OcbZBtXB4u8vFmYzZjTT+42hNWQb0/40Q8uyQadKkoR9BrrsNRwCEDqKNh8lya1BCEajDLxj+XMxi4p]]></req_info></xml>';
//        db('errow')->insertGetId(array('info'=>$xml));die();
        $array_data = json_decode(json_encode(simplexml_load_string($xml, 'SimpleXMLElement', LIBXML_NOCDATA)), true);
        $req_infoB = base64_decode($array_data['req_info'], true);//base64解密码
        $req_infoC = openssl_decrypt($req_infoB, 'aes-256-ecb', md5($this->wx_config['wxkey']), OPENSSL_RAW_DATA);//使用加密的md5进行aes-256-ecb解密，获得xml格式数据
        $req_infoD = $this->xmlToArray($req_infoC);//xml转数组
        
        if ($array_data['return_code'] == 'SUCCESS') {
            if($req_infoD['refund_status'] == 'SUCCESS'){
                $pay = db('pay')->where(['sn' => $req_infoD['out_trade_no']])->find();

                if ($pay) {
                    if ($pay['status'] == 5) {
                        $pay_data = array(
                            'status' => 3,
                            'refund_time' => time(),
                            'refund_id' => $req_infoD['refund_id'],
                        );
                        $res = db('pay')->where(['id' => $pay['id']])->update($pay_data);
                        if ($res) {
                            $user_data = array(
                                'deposit' => 0,
                                'deposit_id' => 0,
                            );
                            db('user')->where(['id' => $pay['user_id']])->update($user_data);
                            
                            $this->refundCost($pay['id']);//扣除手续费
                            //$this->share();//利润分配到账
                        }
                    }
                }
            }else{
                db('pay')->where(['sn' => $req_infoD['out_trade_no']])->update(array('status'=>2));
            }
        }
    }
    
    //退款手续费处理
    public function refundCost($pay_id){
//        $pay_id = 6843;
        $pay = db('pay')->where(['id' => $pay_id])->find();
        if($pay['is_refundcost'] == 1){
            $Service_Charge = sprintf("%.2f",$pay['money'] * 0.006);//手续费
            $order_money = $pay['order_money'];//总收益
            $order_beneficiary = json_decode($pay['order_beneficiary'],true);
            if($order_money > 0 && is_array($order_beneficiary)){
                
                foreach ($order_beneficiary as $k => $v){
                    $money = sprintf("%.2f",$Service_Charge * ( $order_beneficiary[$k]['money'] / $order_money ));
                    $branch_data = array(
                        'types' => $order_beneficiary[$k]['types'],
                        'member_id' => $order_beneficiary[$k]['member_id'],
                        'order_money' => $Service_Charge,
                        'fcbl' => $order_beneficiary[$k]['money'] / $order_money ,
                        'money' => '-'.$money,
                        'createtime' => time(),
                        'change_types' => 2,
                        'is_toaccount' => 1,
                        'pay_id' =>$pay['id'],
                    );
                    $res = db('branch')->insertGetId($branch_data);
                }
            }
            db('pay')->where(['id' => $pay['id']])->update(['is_refundcost' => 2]);
        }
    }
    
    //xml转换成数组  
    private function xmlToArray($xml) {
	//禁止引用外部xml实体   
	libxml_disable_entity_loader(true);
	$xmlstring = simplexml_load_string($xml, 'SimpleXMLElement', LIBXML_NOCDATA);
	$val = json_decode(json_encode($xmlstring), true);
	return $val;
    }
    
    //支付回调
    public function notify(){       
        
        $xml = file_get_contents("php://input");
        $array_data = json_decode(json_encode(simplexml_load_string($xml, 'SimpleXMLElement', LIBXML_NOCDATA)), true);
        $pay = db('pay')->where(['sn'=>$array_data['out_trade_no']])->find();//
        if($pay){
            if($pay['status'] == 1){
//                if($pay['money'] == $array_data['cash_fee']){
                    $pay_data = array(
                        'pay_type' => 1,
                        'pay_time' => time(),
                        'transaction_id' => $array_data['transaction_id'],
                        'status' => 2
                    );
                    $res = db('pay')->where(['id'=>$pay['id']])->update($pay_data);
                    
                    if($res){
                        if($pay['types'] == 1){
                            db('user')->where(['id'=>$pay['user_id']])->update(array('deposit'=>$pay['money'],'deposit_id'=>$pay['id']));
                        }
                        
                        if($pay['types'] == 2){
                            $order = db('order')->where(['id'=>$pay['order_id']])->find();
                            $order_data = array(
                                'status' => 3,
                                'pay_types' => 1,
                                'pay_status' => 1,
                                'pay_time' => time(),
                            );
                            $res = db('order')->where(['id'=>$order['id']])->update($order_data);
                            if($res){
                                $this->branchCommission($order['id']);
                            }
                        }
                        
                        if($pay['types'] == 3){
                            $setmeal_log = db('setmeal_log')->where(['id'=>$pay['setmeallog_id']])->find();
                            $setmeal = db('setmeal')->where(['id' => $setmeal_log['setmeal_id']])->find();
                            $effective_end = time() + ($setmeal['duration'] * 86400);
                            $log_data = [
                                'effective_start' => time(),
                                'effective_end' => $effective_end,
                                'status' => 2,
                            ];
                            db('setmeal_log')->where(['id' => $setmeal_log['id']])->update($log_data);
                        }

                        // 余额充值处理
                        if($pay['types'] == 4){
                            // 记录充值回调开始日志
                            $log_data = [
                                'type' => 'recharge_callback_start',
                                'pay_id' => $pay['id'],
                                'user_id' => $pay['user_id'],
                                'amount' => $pay['money'],
                                'order_id' => $pay['order_id'],
                                'transaction_id' => $array_data['transaction_id'],
                                'time' => date('Y-m-d H:i:s'),
                                'data' => $array_data
                            ];
                            trace($log_data, '充值回调处理开始');

                            try {
                                // 更新充值订单状态
                                $recharge_order_data = [
                                    'status' => '2', // 已完成
                                    'pay_id' => $pay['id'],
                                    'pay_time' => time(),
                                    'updatetime' => time()
                                ];
                                $recharge_update_result = db('recharge_order')->where(['id' => $pay['order_id']])->update($recharge_order_data);
                                //查询充值订单信息
                                $order = Db::name('recharge_order')->where(['id' => $pay['order_id']])->find();

                                //充值总金额= 充值金额+赠送金额
                                $total_money = jia($order['amount'],$order['giftmoney']);
                                // 记录充值订单更新结果
                                $log_data['type'] = 'recharge_order_update';
                                $log_data['update_result'] = $recharge_update_result;
                                trace($log_data, '充值订单状态更新');

                                // 获取用户当前余额
                                $user_before = db('user')->where(['id' => $pay['user_id']])->find();
                                $log_data['user_money_before'] = $user_before['money'];

                                // 更新用户余额并记录变动日志
                                \app\common\model\User::money($total_money, $pay['user_id'], '余额充值', 1, $pay['order_id']);

                                // 获取用户更新后余额
                                $user_after = db('user')->where(['id' => $pay['user_id']])->find();
                                $log_data['user_money_after'] = $user_after['money'];
                                $log_data['type'] = 'recharge_success';
                                trace($log_data, '充值回调处理成功');

                            } catch (\Exception $e) {
                                // 记录错误日志
                                $log_data['type'] = 'recharge_error';
                                $log_data['error'] = $e->getMessage();
                                $log_data['trace'] = $e->getTraceAsString();
                                trace($log_data, '充值回调处理异常');
                            }
                        }
                    }
//                }
            }
        }
    }
    
    public function branchCommission($order_id){
        $order_info = db('order')->where(['id'=>$order_id])->find();
        if($order_info){
            if($order_info['is_branch'] == 1){
                if($order_info['status'] == 3 && $order_info['pay_status'] == 1){
                    $order_info['Service_Charge'] = 0.006;//手续费比例
                    
                    /*
                     * 分润步骤
                     * 1、医院分润
                     * 2、代理商分润
                     * 3、平台分润
                     */

                    //查询设备所属医院
                    $hospital = db('hospital')->where(['id' => $order_info['hospital_id']])->find();
                    if ($hospital['fcbl'] > 0) {
                        $this->hospitalBranch($order_info, $hospital);
                    }

                    $lowrFrbl = $this->agentBranch($hospital['fcbl'], $hospital['route'], $order_info);

                    $this->platformBranch($lowrFrbl, $hospital['platform_id'], $order_info);

                    db('order')->where(['id' => $order_info['id']])->update(array('is_branch' => 2));
                }else{
                    die('不符合分佣条件');
                }
            }else{
                die('已分佣');
            }
        }else{
            die('订单不存在');
        }
    }
    
    //医院分润
    public function hospitalBranch($order,$hospital){
        $money = $order['money'] - ( $order['money'] * $order['Service_Charge']);
        $money = ( $money / 100 ) * $hospital['fcbl'];
        $res = $this->branchAdd(3, $hospital['id'], $order['id'], $hospital['fcbl'], $money,$order['Service_Charge'],1);
//        if($res){
//            db('hospital')->where(['id'=>$hospital['id']])->setInc('balance', $money);
//        }
    }
    
    /*
     * 代理商分润
     * lowrFrbl 下级分润比例
     * route 分润层级
     * order 订单信息
     */
    public function agentBranch($lowrFrbl, $route, $order) {
        $routeArr = json_decode($route, true);

        foreach ($routeArr as $k => $v) {
            if ($routeArr[$k]['fcbl'] > $lowrFrbl) {
                $fcbl = $routeArr[$k]['fcbl'] - $lowrFrbl;
                $lowrFrbl = $routeArr[$k]['fcbl'];
                $money = $order['money'] - ( $order['money'] * $order['Service_Charge']);
                $money = ( $money / 100 ) * $fcbl;
                $res = $this->branchAdd(2, $routeArr[$k]['id'], $order['id'], $fcbl, $money, $order['Service_Charge'], 1);
//                if ($res) {
//                    db('agent')->where(['id' => $routeArr[$k]['id']])->setInc('balance', $money);
//                }
            }
        }
        return $lowrFrbl;
    }
    
    /*
     * 子平台分润
     */
    public function platformBranch($lowrFrbl, $platform_id, $order) {
        $platform = db('platform')->where(['id' => $platform_id])->find();
        $fcbl = $platform['fcbl'] - $lowrFrbl;
        if ($fcbl > 0) {
            $money = $order['money'] - ( $order['money'] * $order['Service_Charge']);
            $money = ( $money / 100 ) * $fcbl;
            $res = $this->branchAdd(1, $platform['id'], $order['id'],$fcbl, $money, $order['Service_Charge'], 1);
//            if ($res) {
//                db('platform')->where(['id' => $platform['id']])->setInc('balance', $money);
//            }
        }
    }
    
    /**
     * 生成分润记录
     * @param int $types 分润类型:1=子平台,2=代理商,3=医院
     * @param int $member_id 变动人ID
     * @param int $order_id 订单id
     * @param int $fcbl 分成比例
     * @param int $money 实际收益
     * @param int $service_charge 手续费
     * @param int $change_types 变动类型:1=分润,2=手续费扣除
     */
    public function branchAdd($types,$member_id,$order_id,$fcbl,$money,$service_charge,$change_types){
        $order_info = db('order')->where(['id'=>$order_id])->find();
        $money = sprintf("%.2f",$money);
        
        $branch_data = array(
            'platform_id' => $order_info['platform_id'],
            'agent_id' => $order_info['agent_id'],
            'hospital_id' => $order_info['hospital_id'],
            'departments_id' => $order_info['departments_id'],
            'equipment_id' => $order_info['equipment_id'],
            'equipment_info_id' => $order_info['equipment_info_id'],
            'order_id' => $order_id,
            'types' => $types,
            'member_id' => $member_id,
            'order_money' => $order_info['money'],
            'fcbl' => $fcbl,
            'money' => $money,
            'createtime' => time(),
            'service_charge' => $service_charge,
            'change_types' => $change_types,
            'is_toaccount' => 1,
        );
        $res = db('branch')->insertGetId($branch_data);
                
        //记录本次缴纳押金的受益人
        $user = db('user')->where(['id' => $order_info['user_id']])->find();
        $pay = db('pay')->where(['id' => $user['deposit_id']])->find();
        $is = true;//是否是第一有收益 false 不是 true 是
        if($pay['order_beneficiary'] != ''){
            $order_beneficiary = json_decode($pay['order_beneficiary'],true);
            foreach($order_beneficiary as $k => $v){
                if($order_beneficiary[$k]['types'] == $types && $order_beneficiary[$k]['member_id'] == $member_id){
                    //不是第一次收益
                    $is = false;
                    $order_beneficiary[$k]['money'] += $money;
                }
            }
        }else{
            $order_beneficiary = array();
        }
        if($is){
            $order_beneficiary[] = array(
                'types' => $types,
                'member_id' => $member_id,
                'money' => $money,
            );
        }
        $order_money = $pay['order_money'] + $money;
        db('pay')->where(['id' => $pay['id']])->update([
            'order_beneficiary' => json_encode($order_beneficiary),
            'order_money' => $order_money,
            ]);
        return $res;
    }
    
    /**
     * 分润导到账处理
     */
    public function share(){
        $branch = db('branch')->where(['createtime' => ['<=',  time()],'is_toaccount' => 1])->select();
        foreach($branch as $k => $v){
            $surface = '';
            switch ($branch[$k]['types']) {
                case 1:
                    $surface = 'platform';
                    break;
                case 2:
                    $surface = 'agent';
                    break;
                case 3:
                    $surface = 'hospital';
                    break;
                default:
                    break;
            }
            if($surface != ''){
                $res = db($surface)->where(['id' => $branch[$k]['member_id']])->setInc('balance',$branch[$k]['money']);
                if($res){
                    db('branch')->where(['id' => $branch[$k]['id']])->update(['is_toaccount' => 2]);
                }
//                if($branch[$k]['change_types'] == 1){
//                    
//                }else{
//                    
//                }
            }
        }
    }


    public function a(){
        $list = db('order')
                ->where(['hospital_id' => 27,'pay_status' => 1,'status' => 3])
//                ->where(['hospital_fcbl'=>0])
//                ->where(['hospital_fcbl'=>['>',0]])
                ->select();
        foreach($list as $k => $v){
            $list[$k]['branch'] = db('branch')
                    ->where(['order_id'=> $list[$k]['id']])
//                    ->where(['types' => 2])
                    ->select();
//            if($list[$k]['branch'][0]['fcbl'] != 90){
//                print_r($list[$k]['branch'][0]);
//            }
        }
        print_r($list);
        
        $sum = db('order')
                ->where(['hospital_id' => 27,'pay_status' => 1,'status' => 3])
//                ->where(['hospital_fcbl'=>0])
//                ->where(['hospital_fcbl'=>['>',0]])
                ->sum('money');
        
        
//        $sum = db('branch')->where(['platform_id'=>15,'types'=>1])->sum('money');
        
        die('==' . $sum);
    }
}
