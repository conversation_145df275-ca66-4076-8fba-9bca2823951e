<?php

namespace app\api\controller;

use app\common\controller\Api;
use think\Db;
use app\common\logic\Equipment;
use Endroid\QrCode\QrCode;
use MyImages;
/**
 * 示例接口
 */
class Demo extends Api
{

    //如果$noNeedLogin为空表示所有接口都需要登录才能请求
    //如果$noNeedRight为空表示所有接口都需要验证权限才能请求
    //如果接口已经设置无需登录,那也就无需鉴权了
    //
    // 无需登录的接口,*表示全部
    protected $noNeedLogin = '*';
    // 无需鉴权的接口,*表示全部
    protected $noNeedRight = ['test2'];

    /**
     * 测试方法
     *
     * @ApiTitle    (测试名称)
     * @ApiSummary  (测试描述信息)
     * @ApiMethod   (POST)
     * @ApiRoute    (/api/demo/test/id/{id}/name/{name})
     * @ApiHeaders  (name=token, type=string, required=true, description="请求的Token")
     * @ApiParams   (name="id", type="integer", required=true, description="会员ID")
     * @ApiParams   (name="name", type="string", required=true, description="用户名")
     * @ApiParams   (name="data", type="object", sample="{'user_id':'int','user_name':'string','profile':{'email':'string','age':'integer'}}", description="扩展数据")
     * @ApiReturnParams   (name="code", type="integer", required=true, sample="0")
     * @ApiReturnParams   (name="msg", type="string", required=true, sample="返回成功")
     * @ApiReturnParams   (name="data", type="object", sample="{'user_id':'int','user_name':'string','profile':{'email':'string','age':'integer'}}", description="扩展数据返回")
     * @ApiReturn   ({
         'code':'1',
         'msg':'返回成功'
        })
     */

    public function index(){
        phpinfo();
    }
    public function test()
    {
        $this->success('返回成功', $this->request->param());
    }

    /**
     * 无需登录的接口
     *
     */
    public function test1()
    {
        $this->success('返回成功', ['action' => 'test1']);
    }

    /**
     * 需要登录的接口
     *
     */
    public function test2()
    {
        $this->success('返回成功', ['action' => 'test2']);
    }

    /**
     * 需要登录且需要验证有相应组的权限
     *
     */
    public function test3()
    {
        $this->success('返回成功', ['action' => 'test3']);
    }


    /**
     * 测试方法5：从API模块调用Logic发送指令
     *
     * @ApiTitle    (测试5：发送地锁指令)
     * @ApiSummary  (用于测试从API模块调用通用的EquipmentLogic来发送MQTT指令)
     * @ApiMethod   (GET)
     * @ApiRoute    (/api/demo/test5)
     * @ApiParams   (name="sn", type="string", required=true, description="要操作的地锁SN码")
     * @ApiParams   (name="action", type="string", required=true, sample="open 或 close", description="要执行的操作")
     * @ApiReturn   ({
    'code':'1',
    'msg':'指令已发送，请等待设备响应',
    "data": []
    })
     */
    public function test5()
    {
        // --- 1. API控制器职责：获取并验证请求参数 ---
//        $sn = $this->request->request("sn");
//        if (!$sn) {
//            $this->error('参数错误: 缺少 sn');
//        }
//
//        $action = $this->request->request("action");
//        if (!in_array($action, ['open', 'close'])) {
//            $this->error('参数错误: action 必须是 open 或 close');
//        }

        $sn=710010;
        $action='open';

        // 验证设备是否存在于数据库中 (这是一个好习惯)
        $equipmentExists = Db::name('equipment')->where('sn', $sn)->count();
        if (!$equipmentExists) {
            $this->error('操作失败: 数据库中不存在SN为 ' . $sn . ' 的设备');
        }

        // --- 2. API控制器职责：调用逻辑层处理核心业务 ---
        $logic = new Equipment();
        $result = $logic->sendSwitchCommand($sn, $action);

        // --- 3. API控制器职责：根据逻辑层返回的结果，向客户端返回JSON响应 ---
        if ($result['code'] === 1) {
            // Logic层执行成功
            $this->success($result['msg']);
        } else {
            // Logic层执行失败
            $this->error($result['msg']);
        }
    }

    /**
     * 测试方法6：从API模块调用Logic发送查询状态指令
     *
     * @ApiTitle    (测试6：查询地锁状态)
     * @ApiSummary  (用于测试从API模块调用通用的EquipmentLogic来发送查询状态指令)
     * @ApiMethod   (GET)
     * @ApiRoute    (/api/demo/test6)
     * @ApiParams   (name="sn", type="string", required=true, description="要查询的地锁SN码")
     * @ApiReturn   ({
    'code':'1',
    'msg':'查询指令已发送，请等待设备上报状态',
    "data": []
    })
     */
    public function test6()
    {
        // --- 1. API控制器职责：获取并验证请求参数 ---
//        $sn = $this->request->request("sn");
//        if (!$sn) {
//            $this->error('参数错误: 缺少 sn');
//        }
$sn=710020;
        // 验证设备是否存在于数据库中
        $equipmentExists = Db::name('equipment')->where('sn', $sn)->count();
        if (!$equipmentExists) {
            $this->error('操作失败: 数据库中不存在SN为 ' . $sn . ' 的设备');
        }

        // --- 2. API控制器职责：调用逻辑层处理核心业务 ---
        $logic = new Equipment();
        $result = $logic->queryDeviceStatus($sn);

        // --- 3. API控制器职责：根据逻辑层返回的结果，向客户端返回JSON响应 ---
        if ($result['code'] === 1) {
            // Logic层执行成功
            $this->success($result['msg']);
        } else {
            // Logic层执行失败
            $this->error($result['msg']);
        }
    }
    /**
     * 批量生成设备二维码
     *
     * @ApiTitle    (批量生成设备二维码)
     * @ApiSummary  (批量生成710001-710019共19个设备的二维码，底部显示设备编号)
     * @ApiMethod   (GET)
     * @ApiRoute    (/api/demo/batchGenerateQrCodes)
     * @ApiParams   (name="domain", type="string", required=false, description="域名，默认使用当前域名")
     * @ApiReturn   ({
        'code':'1',
        'msg':'批量生成完成',
        'data': {
            'success_count': 19,
            'failed_count': 0,
            'results': []
        }
        })
     */
    public function batchGenerateQrCodes()
    {
        // 获取域名参数，默认使用当前域名
        $domain = $this->request->get('domain', $_SERVER['HTTP_HOST']);

        // 设备编号范围：710001-710019 710021~710320
        $startNum = 710021;
        $endNum = 710320;

        $results = [];
        $successCount = 0;
        $failedCount = 0;

        // 确保batch目录存在
        $batchDir = ROOT_PATH . 'public/uploads/ewm/batch/';
        if (!is_dir($batchDir)) {
            mkdir($batchDir, 0755, true);
        }

        // 循环生成每个设备的二维码
        for ($deviceNum = $startNum; $deviceNum <= $endNum; $deviceNum++) {
            try {
                $result = $this->generateSingleQrCode($deviceNum, $domain);
                if ($result['success']) {
                    $successCount++;
                    $results[] = [
                        'device_no' => $deviceNum,
                        'status' => 'success',
                        'qr_path' => $result['qr_path'],
                        'final_image_path' => $result['final_image_path'],
                        'final_image_url' => $result['final_image_url']
                    ];
                } else {
                    $failedCount++;
                    $results[] = [
                        'device_no' => $deviceNum,
                        'status' => 'failed',
                        'error' => $result['error']
                    ];
                }
            } catch (\Exception $e) {
                $failedCount++;
                $results[] = [
                    'device_no' => $deviceNum,
                    'status' => 'failed',
                    'error' => $e->getMessage()
                ];
            }
        }

        $this->success('批量生成完成', [
            'success_count' => $successCount,
            'failed_count' => $failedCount,
            'total_count' => $endNum - $startNum + 1,
            'results' => $results
        ]);
    }

    /**
     * 生成单个设备的二维码
     *
     * @param int $deviceNum 设备编号
     * @param string $domain 域名
     * @return array
     */
    private function generateSingleQrCode($deviceNum, $domain)
    {
        try {
            // 1. 生成基础二维码
            $qrUrl = 'https://' . $domain . '/kpl/' . $deviceNum;
            $qrCode = new QrCode();
            $qrCode->setText($qrUrl)->setSize(512);

            // 生成二维码文件名（直接使用设备编号）
            $qrName = 'qr_' . $deviceNum . '.png';
            $qrPath = ROOT_PATH . 'public/uploads/ewm/' . $qrName;

            // 保存基础二维码
            $qrCode->save($qrPath);

            // 2. 在二维码中心添加logo
            $logoResult = $this->addLogoToQrCode($qrPath);
            if (!$logoResult['success']) {
                return [
                    'success' => false,
                    'error' => '添加logo失败: ' . $logoResult['error']
                ];
            }

            $qrRelativePath = 'uploads/ewm/' . $qrName;

            // 3. 创建最终的二维码标牌
            $finalImageName = 'final_qr_' . $deviceNum . '.png';
            $finalImagePath = ROOT_PATH . 'public/uploads/ewm/batch/' . $finalImageName;

            // 使用新的方法生成二维码标牌
            $result = $this->createQrCodePlate($qrRelativePath, $deviceNum, $finalImagePath);

            if ($result['success']) {
                $finalImageRelativePath = 'uploads/ewm/batch/' . $finalImageName;
                return [
                    'success' => true,
                    'qr_path' => $qrRelativePath,
                    'final_image_path' => $finalImageRelativePath,
                    'final_image_url' => 'http://' . $domain . '/' . $finalImageRelativePath
                ];
            } else {
                return [
                    'success' => false,
                    'error' => $result['error']
                ];
            }

        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    /**
     * 【终极完美版】使用 Imagick 库，可调logo大小和留白，并自动裁剪logo透明边框
     *
     * @param string $qrPath 二维码文件路径
     * @return array
     */
    private function addLogoToQrCode($qrPath)
    {
        try {
            // --- 1. 参数配置 (你现在可以自由修改这里了) ---
            $logoSizeRatio = 0.20; // 将logo大小设置为二维码的30%，你可以调整这个值
            $padding = 7;         // logo周围的白色留白大小，单位像素

            // --- 2. 加载图片 ---
            $logoPath = ROOT_PATH . 'public/aa.png';
            if (!file_exists($logoPath)) return ['success' => false, 'error' => 'Logo文件 aa.png 不存在'];

            $qr = new \Imagick($qrPath);
            $logo = new \Imagick($logoPath);

            // --- 3. 【关键步骤】自动裁剪Logo的透明边框 ---
            // 参数0代表默认的模糊度，对于纯透明边框非常有效
            $logo->trimImage(0);

            // --- 4. 计算尺寸 (在裁剪后进行) ---
            $qrWidth = $qr->getImageWidth();
            $qrHeight = $qr->getImageHeight(); // 获取二维码高度，避免潜在问题
            $newLogoWidth = (int)($qrWidth * $logoSizeRatio);
            $logo->thumbnailImage($newLogoWidth, 0);

            // 获取【裁剪并缩放后】的logo精确尺寸
            $logoWidth = $logo->getImageWidth();
            $logoHeight = $logo->getImageHeight();

            // --- 5. 创建并绘制白色的“留白底座” ---
            $draw = new \ImagickDraw();
            $draw->setFillColor('white');

            // 根据裁剪后的logo尺寸，精确计算白色底座的坐标和尺寸
            $rectX1 = (int)(($qrWidth - $logoWidth) / 2 - $padding);
            $rectY1 = (int)(($qrHeight - $logoHeight) / 2 - $padding);
            $rectX2 = (int)(($qrWidth + $logoWidth) / 2 + $padding);
            $rectY2 = (int)(($qrHeight + $logoHeight) / 2 + $padding);
            $cornerRadius = 20;

            $draw->roundRectangle($rectX1, $rectY1, $rectX2, $rectY2, $cornerRadius, $cornerRadius);
            $qr->drawImage($draw);

            // --- 6. 叠加Logo ---
            $logoX = (int)(($qrWidth - $logoWidth) / 2);
            $logoY = (int)(($qrHeight - $logoHeight) / 2);
            $qr->compositeImage($logo, \Imagick::COMPOSITE_OVER, $logoX, $logoY);

            // --- 7. 保存和释放资源 ---
            $qr->writeImage($qrPath);
            $qr->destroy();
            $logo->destroy();
            $draw->destroy();

            return ['success' => true];

        } catch (\Exception $e) {
            return ['success' => false, 'error' => '[Imagick] ' . $e->getMessage()];
        }
    }

    /**
     * 创建二维码标牌（新设计：正方形，无背景图，底部显示"共享地锁 编号"）
     *
     * @param string $qrPath 二维码路径
     * @param int $deviceNum 设备编号
     * @param string $outputPath 输出文件路径
     * @return array
     */
    private function createQrCodePlate($qrPath, $deviceNum, $outputPath)
    {
        try {
            // 创建正方形画布 (650x650)，按图纸比例放大10倍
            $canvasSize = 650;
            $im = imagecreatetruecolor($canvasSize, $canvasSize);

            // 填充白色背景
            $white = imagecolorallocate($im, 255, 255, 255);
            imagefill($im, 0, 0, $white);

            // 字体文件路径
            $font_bold = ROOT_PATH . "public/uploads/ewm/msyhbd.ttf";

            // 设定字体颜色
            $black = imagecolorallocate($im, 0, 0, 0);

            // 二维码尺寸和位置（500x500，居中）
            $qrSize = 500;
            $qrX = ($canvasSize - $qrSize) / 2;  // 75
            $qrY = 50;  // 距离顶部50像素

            // 读取并放置二维码
            $qrImagePath = 'http://' . $_SERVER['HTTP_HOST'] . '/' . $qrPath;
            list($qr_w, $qr_h) = getimagesize($qrImagePath);
            $qrImg = $this->createImageFromFile($qrImagePath);
            if ($qrImg) {
                imagecopyresized($im, $qrImg, $qrX, $qrY, 0, 0, $qrSize, $qrSize, $qr_w, $qr_h);
                imagedestroy($qrImg);
            }

            // 底部文字区域 - 一行显示"共享地锁 710001"
            $textY = $qrY + $qrSize + 40;  // 二维码下方40像素

            // 组合文字：共享地锁 + 空格 + 设备编号
            $fullText = '共享地锁 ' . $deviceNum;
            $fontSize = 30;

            // 计算文字位置（居中）
            $textBox = imagettfbbox($fontSize, 0, $font_bold, $fullText);
            $textWidth = $textBox[4] - $textBox[0];
            $textX = ($canvasSize - $textWidth) / 2;

            // 绘制完整文字
            imagettftext($im, $fontSize, 0, $textX, $textY, $black, $font_bold, $fullText);

            // 保存图片
            $result = imagepng($im, $outputPath);

            // 释放空间
            imagedestroy($im);

            if ($result && file_exists($outputPath)) {
                return ['success' => true];
            } else {
                return ['success' => false, 'error' => '图片保存失败'];
            }

        } catch (\Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }





    /**
     * 从图片文件创建Image资源
     *
     * @param string $url 图片URL
     * @return resource|false
     */
    private function createImageFromFile($url)
    {
        $imageInfo = getimagesize($url);
        if (!$imageInfo) {
            return false;
        }

        switch ($imageInfo[2]) {
            case IMAGETYPE_JPEG:
                return imagecreatefromjpeg($url);
            case IMAGETYPE_PNG:
                return imagecreatefrompng($url);
            case IMAGETYPE_GIF:
                return imagecreatefromgif($url);
            default:
                return false;
        }
    }





}
